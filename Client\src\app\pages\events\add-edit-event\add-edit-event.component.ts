import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { finalize } from 'rxjs/operators';
import { EventsService } from '../../../Core/Services/events.service';
import { AuthService } from '../../../Core/Services/auth.service';
import { DateUtilsService } from '../../../Core/Services/date-utils.service';
import {
  Event,
  EventRequest,
  EventType,
  Category,
  EventLocationType,
  EventStatus,
} from '../../../Core/Models/events';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-add-edit-event',
  standalone: false,
  templateUrl: './add-edit-event.component.html',
  styleUrl: './add-edit-event.component.scss',
  providers: [MessageService],
})
export class AddEditEventComponent implements OnInit {
  eventForm!: FormGroup;
  isLoading = false;
  isSubmitting = false;
  error: string | null = null;
  eventId: number | null = null;
  isEditMode = false;
  formSubmitted = false;
  imagePreview: string | null = null;
  eventImageUrl: string | null = null;
  selectedFile: File | null = null;
  isAdmin = false;
  private apiUrl = environment.apiUrl;

  // Enums for the form
  eventTypes = EventType;
  categories = Category;
  locationTypes = EventLocationType;

  // Dropdown options
  eventTypeOptions: { name: string; value: number }[] = [
    { name: 'Appearance Or Signing', value: EventType.AppearanceOrSigning },
    { name: 'Attraction', value: EventType.Attraction },
    { name: 'Camp Trip Or Retreat', value: EventType.CampTripOrRetreat },
    {
      name: 'Class Training Or Workshop',
      value: EventType.ClassTrainingOrWorkshop,
    },
    { name: 'Concert Or Performance', value: EventType.ConcertOrPerformance },
    { name: 'Conference', value: EventType.Conference },
    { name: 'Convention', value: EventType.Convention },
    { name: 'Dinner Or Gala', value: EventType.DinnerOrGala },
    { name: 'Festival Or Fair', value: EventType.FestivalOrFair },
    { name: 'Games Or Competition', value: EventType.GamesOrCompetition },
    {
      name: 'Meeting Or Networking Event',
      value: EventType.MeetingOrNetworkingEvent,
    },
    { name: 'Other', value: EventType.Other },
    {
      name: 'Party Or Social Gathering',
      value: EventType.PartyOrSocialGathering,
    },
    { name: 'Rally', value: EventType.Rally },
    { name: 'Screening', value: EventType.Screening },
    { name: 'Seminar Or Talk', value: EventType.SeminarOrTalk },
    { name: 'Tour', value: EventType.Tour },
    { name: 'Tournament', value: EventType.Tournament },
    {
      name: 'Trade Show Consumer Show Or Expo',
      value: EventType.TradeShowConsumerShowOrExpo,
    },
  ];

  categoryOptions: { name: string; value: number }[] = [
    { name: 'Careers And Employment', value: Category.CareersAndEmployment },
    { name: 'Community Resources', value: Category.CommunityResources },
    { name: 'Early Childhood', value: Category.EarlyChildhood },
    { name: 'Health Wellness', value: Category.HealthWellness },
    { name: 'Maternal Health Care', value: Category.MaternalHealthCare },
    { name: 'Rental Housing', value: Category.RentalHousing },
  ];

  locationTypeOptions: { label: string; value: number }[] = [
    { label: 'Venue', value: EventLocationType.Venue },
    { label: 'Online Event', value: EventLocationType.Online },
  ];

  // Date validation
  minDate: Date = new Date();

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private eventsService: EventsService,
    private messageService: MessageService,
    private authService: AuthService,
    private dateUtils: DateUtilsService,
  ) {}

  ngOnInit(): void {
    this.minDate = new Date();
    this.minDate.setHours(0, 0, 0, 0);

    this.initForm();
    this.checkUserRole();
    this.setupEventMode();
    this.setupFormListeners();
  }

  setupEventMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.eventId = Number(id);
      this.isEditMode = true;
      this.loadEventData(Number(id));
    } else {
      this.setDefaultValues();
    }
  }

  setDefaultValues(): void {
    const today = new Date();
    const endDate = new Date(today);
    endDate.setHours(endDate.getHours() + 1);

    this.getDateTimeControl('eventStarts')?.setValue(today);
    this.getDateTimeControl('eventEnds')?.setValue(endDate);
    this.getDateTimeControl('startTime')?.setValue(today);
    this.getDateTimeControl('endTime')?.setValue(endDate);
  }

  setupFormListeners(): void {
    // Add listeners for date changes
    this.getDateTimeControl('eventStarts')?.valueChanges.subscribe((date) => {
      const endDate = this.getDateTimeControl('eventEnds')?.value;
      if (endDate && date > endDate) {
        this.getDateTimeControl('eventEnds')?.setValue(date);
      }
    });

    // Add listeners for display time checkboxes
    this.getDateTimeControl('displayStartTime')?.valueChanges.subscribe(
      (checked) => this.handleDisplayStartTimeChange(checked),
    );

    this.getDateTimeControl('displayEndTime')?.valueChanges.subscribe(
      (checked) => this.handleDisplayEndTimeChange(checked),
    );

    // Initialize time fields based on checkbox values
    this.handleDisplayStartTimeChange(
      this.getDateTimeControl('displayStartTime')?.value,
    );
    this.handleDisplayEndTimeChange(
      this.getDateTimeControl('displayEndTime')?.value,
    );
  }

  // Utility method to get full image path
  getFullImagePath(relativePath: string): string {
    if (!relativePath) return 'assets/images/placeholder.jpg';
    if (relativePath.startsWith('http')) return relativePath;
    if (relativePath.includes('/api/Files/'))
      return `${this.apiUrl}${relativePath}`;
    return `${this.apiUrl}/api/Files/${relativePath.replace(/^\/+/, '')}`;
  }

  checkUserRole(): void {
    const userInfo = this.authService.getUserInfo();
    if (userInfo && userInfo.role) {
      this.isAdmin = ['global admin'].includes(userInfo.role.toLowerCase());
    }
  }

  initForm(): void {
    const defaultType =
      this.eventTypeOptions.length > 0 ? this.eventTypeOptions[0].value : '';
    const defaultCategory =
      this.categoryOptions.length > 0 ? this.categoryOptions[0].value : '';

    this.eventForm = this.fb.group({
      basicInfo: this.fb.group({
        title: ['', [Validators.required]],
        type: [defaultType, [Validators.required]],
        category: [defaultCategory, [Validators.required]],
        capacity: [null],
        description: ['', [Validators.required]],
        requiresRegistration: [false],
      }),
      dateTime: this.fb.group({
        eventStarts: [null, [Validators.required]],
        eventEnds: [null, [Validators.required]],
        startTime: [{ value: null, disabled: false }],
        endTime: [{ value: null, disabled: false }],
        displayStartTime: [true],
        displayEndTime: [true],
      }),
      location: this.fb.group({
        locationType: [EventLocationType.Venue],
        address1: [''],
        address2: [''],
        city: [''],
        state: [''],
        zipCode: [''],
        country: [''],
        meetingId: [''],
        passcode: [''],
      }),
      contactDetails: this.fb.group({
        contactName: ['', [Validators.minLength(2), Validators.maxLength(100)]],
        contactNo: ['', [Validators.pattern('^[0-9]{10}$')]],
        email: [
          '',
          [
            Validators.email,
            Validators.pattern(
              '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
            ),
          ],
        ],
        website: [
          '',
          [
            Validators.pattern(
              'https?:\\/\\/(www\\.)?[a-zA-Z0-9-]+(\\.[a-zA-Z]{2,})+\\/?.*',
            ),
          ],
        ],
      }),
      skipApproval: [false],
    });

    this.setupLocationTypeValidation();
  }

  setupLocationTypeValidation(): void {
    this.getLocationControl('locationType')?.valueChanges.subscribe((value) => {
      const venueFields = ['address1', 'city', 'state', 'zipCode', 'country'];
      const onlineFields = ['meetingId', 'passcode'];

      if (value === EventLocationType.Venue) {
        this.setValidators(venueFields, true);
        this.setValidators(onlineFields, false);
      } else {
        this.setValidators(venueFields, false);
        this.setValidators(onlineFields, true);
      }
    });
  }

  setValidators(fields: string[], required: boolean): void {
    fields.forEach((field) => {
      const control = this.getLocationControl(field);
      if (control) {
        if (required) {
          control.setValidators([Validators.required]);
        } else {
          control.clearValidators();
        }
        control.updateValueAndValidity();
      }
    });
  }

  loadEventData(id: number): void {
    this.isLoading = true;
    this.eventsService
      .getEventById(id)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: (event) => {
          if (event) {
            this.ensureEventDataIntegrity(event);
            this.populateForm(event);
            if (event.eventImageUrl) {
              this.eventImageUrl = this.getFullImagePath(event.eventImageUrl);
            }
          }
        },
        error: (err) => {
          this.error = err.message;
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail:
              err.message || 'Failed to load event data. Please try again.',
          });
        },
      });
  }

  ensureEventDataIntegrity(event: Event): void {
    // Ensure location data exists
    if (!event.location) {
      event.location = {
        address1: '',
        address2: '',
        city: '',
        state: '',
        zipCode: '',
        country: '',
        meetingId: '',
        passcode: '',
      };
    }

    // Ensure location type is set
    if (!event.locationType) {
      event.locationType = EventLocationType.Venue;
    }

    // Determine correct location type based on data
    if (
      event.locationType === EventLocationType.Online &&
      event.location?.address1 &&
      event.location.address1 !== 'N/A'
    ) {
      event.locationType = EventLocationType.Venue;
    }

    if (
      event.locationType === EventLocationType.Venue &&
      event.location?.meetingId &&
      event.location.meetingId !== 'N/A'
    ) {
      event.locationType = EventLocationType.Online;
    }
  }

  populateForm(event: Event): void {
    // Basic Info
    this.getBasicInfoControl('title')?.setValue(event.title);
    this.setEventType(event);
    this.setEventCategory(event);

    // Set capacity as number
    const capacity =
      event.capacity !== undefined ? Number(event.capacity) : null;
    this.getBasicInfoControl('capacity')?.setValue(capacity);

    this.getBasicInfoControl('description')?.setValue(event.description);
    this.getBasicInfoControl('requiresRegistration')?.setValue(
      event.requiresRegistration,
    );

    // Date and Time
    this.setDateTimeValues(event);

    // Location
    this.getLocationControl('locationType')?.setValue(event.locationType);
    this.setLocationValues(event);

    // Contact Details
    this.setContactDetails(event);

    // Admin approval
    if (this.isAdmin) {
      this.eventForm.get('skipApproval')?.setValue(event.isApproved);
    }
  }

  setEventType(event: Event): void {
    let eventType: number | undefined;

    if (typeof event.type === 'string') {
      eventType = parseInt(event.type as string, 10);
    } else if (typeof event.type === 'number') {
      eventType = event.type;
    } else if (event.typeName) {
      const typeOption = this.eventTypeOptions.find(
        (option) => option.name.toLowerCase() === event.typeName?.toLowerCase(),
      );
      if (typeOption) eventType = typeOption.value;
    }

    if (eventType !== undefined) {
      this.getBasicInfoControl('type')?.setValue(eventType);
      this.getBasicInfoControl('type')?.updateValueAndValidity();
    }
  }

  setEventCategory(event: Event): void {
    let eventCategory: number | undefined;

    if (typeof event.category === 'string') {
      const parsedCategory = parseInt(event.category, 10);
      if (!isNaN(parsedCategory)) {
        eventCategory = parsedCategory;
      } else {
        const categoryOption = this.categoryOptions.find(
          (option) =>
            option.name.toLowerCase() === event.category.toLowerCase(),
        );
        if (categoryOption) eventCategory = categoryOption.value;
      }
    }

    if (eventCategory !== undefined) {
      this.getBasicInfoControl('category')?.setValue(eventCategory);
      this.getBasicInfoControl('category')?.updateValueAndValidity();
    }
  }

  setDateTimeValues(event: Event): void {
    this.getDateTimeControl('eventStarts')?.setValue(
      new Date(event.eventStarts),
    );
    this.getDateTimeControl('eventEnds')?.setValue(new Date(event.eventEnds));

    if (event.startTime) {
      const [hours, minutes] = event.startTime.split(':').map(Number);
      const startTime = new Date();
      startTime.setHours(hours, minutes, 0);
      this.getDateTimeControl('startTime')?.setValue(startTime);
    }

    if (event.endTime) {
      const [hours, minutes] = event.endTime.split(':').map(Number);
      const endTime = new Date();
      endTime.setHours(hours, minutes, 0);
      this.getDateTimeControl('endTime')?.setValue(endTime);
    }

    this.getDateTimeControl('displayStartTime')?.setValue(
      event.displayStartTime,
    );
    this.getDateTimeControl('displayEndTime')?.setValue(event.displayEndTime);
  }

  setLocationValues(event: Event): void {
    if (!event.location) return;

    if (event.locationType === EventLocationType.Venue) {
      const venueFields = [
        { name: 'address1', value: event.location.address1 || '' },
        { name: 'address2', value: event.location.address2 || '' },
        { name: 'city', value: event.location.city || '' },
        { name: 'state', value: event.location.state || '' },
        { name: 'zipCode', value: event.location.zipCode || '' },
        { name: 'country', value: event.location.country || '' },
      ];

      venueFields.forEach((field) => {
        this.getLocationControl(field.name)?.setValue(field.value);
        this.getLocationControl(field.name)?.updateValueAndValidity();
      });
    } else if (event.locationType === EventLocationType.Online) {
      this.getLocationControl('meetingId')?.setValue(
        event.location.meetingId || '',
      );
      this.getLocationControl('passcode')?.setValue(
        event.location.passcode || '',
      );

      this.getLocationControl('meetingId')?.updateValueAndValidity();
      this.getLocationControl('passcode')?.updateValueAndValidity();
    }
  }

  setContactDetails(event: Event): void {
    if (!event.contactDetails) return;

    const contactFields = [
      { name: 'contactName', value: event.contactDetails.contactName },
      { name: 'contactNo', value: event.contactDetails.contactNo },
      { name: 'email', value: event.contactDetails.email },
      { name: 'website', value: event.contactDetails.website },
    ];

    contactFields.forEach((field) => {
      this.getContactDetailsControl(field.name)?.setValue(field.value);
    });
  }

  onFileSelected(event: any): void {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;

    const file = input.files[0];
    const maxSizeInBytes = 5 * 1024 * 1024; // 5MB

    if (file.size > maxSizeInBytes) {
      event.target.value = '';
      this.messageService.add({
        severity: 'error',
        summary: 'File Size Error',
        detail: 'File size exceeds the maximum allowed size of 5 MB',
      });
      return;
    }

    this.selectedFile = file;

    // Create a preview
    const reader = new FileReader();
    reader.onload = () => {
      this.imagePreview = reader.result as string;
    };
    reader.readAsDataURL(this.selectedFile);
  }

  onSubmit(): void {
    this.formSubmitted = true;

    if (!this.validateForm()) return;

    this.isSubmitting = true;
    const eventRequest = this.prepareEventRequest();

    // Call the appropriate service method based on mode
    const request = this.isEditMode
      ? this.eventsService.updateEvent(eventRequest)
      : this.eventsService.createEvent(eventRequest);

    request.pipe(finalize(() => (this.isSubmitting = false))).subscribe({
      next: () => {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: this.isEditMode
            ? 'Event updated successfully!'
            : 'Event created successfully!',
        });

        // Navigate back to events list
        setTimeout(() => this.router.navigate(['/events']), 1500);
      },
      error: (err) => {
        this.error = err.message;
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: err.message || 'Failed to save event. Please try again.',
        });
      },
    });
  }

  validateForm(): boolean {
    // Check form validity
    if (this.eventForm.invalid) {
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please fill out all required fields.',
      });
      return false;
    }

    // Validate contact details
    if (!this.validateContactName()) {
      return false;
    }

    if (!this.validateContactNumber()) {
      return false;
    }

    if (!this.validateEmail()) {
      return false;
    }

    // Validate website URL if provided
    if (!this.validateWebsiteUrl()) {
      return false;
    }

    // Check if image is uploaded for new events
    if (!this.isEditMode && !this.selectedFile) {
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please upload an event image.',
      });
      return false;
    }

    // Check file size if provided
    if (this.selectedFile) {
      const maxSizeInBytes = 5 * 1024 * 1024; // 5MB
      if (this.selectedFile.size > maxSizeInBytes) {
        this.messageService.add({
          severity: 'error',
          summary: 'File Size Error',
          detail: 'File size exceeds the maximum allowed size of 5 MB',
        });
        return false;
      }
    }

    // Validate date and time
    return this.validateDateTime();
  }

  validateDateTime(): boolean {
    const now = new Date();
    const eventStarts = this.getDateTimeControl('eventStarts')?.value;
    const eventEnds = this.getDateTimeControl('eventEnds')?.value;
    const startTime = this.getDateTimeControl('startTime')?.value;
    const endTime = this.getDateTimeControl('endTime')?.value;

    // Create full datetime objects for comparison
    const startDateTime = new Date(eventStarts);
    if (startTime) {
      startDateTime.setHours(
        startTime.getHours(),
        startTime.getMinutes(),
        0,
        0,
      );
    }

    const endDateTime = new Date(eventEnds);
    if (endTime) {
      endDateTime.setHours(endTime.getHours(), endTime.getMinutes(), 0, 0);
    }

    // Check if start date/time is in the past for new events
    if (startDateTime < now && !this.isEditMode) {
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Event start date and time cannot be in the past.',
      });
      return false;
    }

    // Check if end date/time is before start date/time
    if (endDateTime < startDateTime) {
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Event end date and time must be after start date and time.',
      });
      return false;
    }

    return true;
  }

  prepareEventRequest(): EventRequest {
    const basicInfo = this.eventForm.get('basicInfo')?.value;
    const dateTime = this.eventForm.get('dateTime')?.value;
    const location = this.eventForm.get('location')?.value;
    const contactDetails = this.eventForm.get('contactDetails')?.value;
    const skipApproval = this.eventForm.get('skipApproval')?.value;

    const eventRequest: EventRequest = {
      // Basic Info
      title: basicInfo.title,
      type: basicInfo.type,
      category: basicInfo.category,
      capacity: basicInfo.capacity,
      description: basicInfo.description,
      requiresRegistration: basicInfo.requiresRegistration,

      // File
      eventImage: this.selectedFile || undefined,

      // Date and Time
      eventStarts: dateTime.eventStarts
        ? this.dateUtils.formatDateForBackend(dateTime.eventStarts)
        : this.dateUtils.formatDateForBackend(new Date()),
      eventEnds: dateTime.eventEnds
        ? this.dateUtils.formatDateForBackend(dateTime.eventEnds)
        : this.dateUtils.formatDateForBackend(new Date()),
      startTime: this.formatTimeIfDisplayed(
        dateTime.displayStartTime,
        dateTime.startTime,
      ),
      endTime: this.formatTimeIfDisplayed(
        dateTime.displayEndTime,
        dateTime.endTime,
      ),
      displayStartTime: dateTime.displayStartTime,
      displayEndTime: dateTime.displayEndTime,

      // Location Type
      locationType: location.locationType,

      // Approval
      skipApproval: skipApproval,
      status: skipApproval ? EventStatus.Approved : EventStatus.Submitted,
      isApproved: skipApproval,
      submittedOn: this.dateUtils.formatDateForBackend(new Date()),

      // Location and Contact Details
      location: this.prepareLocationData(location),
      contactDetails: {
        contactName: contactDetails.contactName || '',
        contactNo: contactDetails.contactNo || '',
        email: contactDetails.email || '',
        website: contactDetails.website || '',
      },
    };

    // Add ID if in edit mode
    if (this.isEditMode && this.eventId) {
      eventRequest.id = this.eventId;
    }

    return eventRequest;
  }

  formatTimeIfDisplayed(
    displayTime: boolean,
    timeValue: Date | null,
  ): string | null {
    if (!displayTime) return null;
    if (!timeValue) return null;

    const date = new Date(timeValue);
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  }

  prepareLocationData(location: any): any {
    if (location.locationType === this.locationTypes.Venue) {
      return {
        address1: location.address1 || '',
        address2: location.address2 || 'N/A',
        city: location.city || '',
        state: location.state || '',
        zipCode: location.zipCode || '',
        country: location.country || '',
        meetingId: 'N/A',
        passcode: 'N/A',
      };
    } else {
      return {
        meetingId: location.meetingId || '',
        passcode: location.passcode || '',
        address1: 'N/A',
        address2: 'N/A',
        city: 'N/A',
        state: 'N/A',
        zipCode: 'N/A',
        country: 'N/A',
      };
    }
  }

  // Helper methods to access form controls
  getBasicInfoControl(name: string) {
    return this.eventForm.get('basicInfo')?.get(name);
  }

  getDateTimeControl(name: string) {
    return this.eventForm.get('dateTime')?.get(name);
  }

  getLocationControl(name: string) {
    return this.eventForm.get('location')?.get(name);
  }

  getContactDetailsControl(name: string) {
    return this.eventForm.get('contactDetails')?.get(name);
  }

  // Time validation methods
  validateStartTime(event: Date): void {
    const today = new Date();
    const selectedDate = this.getDateTimeControl('eventStarts')?.value;

    if (
      selectedDate &&
      this.dateUtils.isSameDay(selectedDate, today) &&
      event < today
    ) {
      const currentTime = new Date();
      this.getDateTimeControl('startTime')?.setValue(currentTime);

      this.messageService.add({
        severity: 'warn',
        summary: 'Invalid Time',
        detail:
          'Cannot select a time in the past. Time has been reset to current time.',
      });
    }
  }

  validateEndTime(event: Date): void {
    const startDate = this.getDateTimeControl('eventStarts')?.value;
    const endDate = this.getDateTimeControl('eventEnds')?.value;
    const startTime = this.getDateTimeControl('startTime')?.value;

    if (
      startDate &&
      endDate &&
      this.dateUtils.isSameDay(startDate, endDate) &&
      startTime &&
      event < startTime
    ) {
      const newEndTime = new Date(startTime);
      newEndTime.setHours(newEndTime.getHours() + 1);
      this.getDateTimeControl('endTime')?.setValue(newEndTime);

      this.messageService.add({
        severity: 'warn',
        summary: 'Invalid Time',
        detail: 'End time must be after start time. Time has been adjusted.',
      });
    }
  }

  // Validate contact name
  validateContactName(): boolean {
    const contactNameControl = this.getContactDetailsControl('contactName');

    if (contactNameControl?.value && contactNameControl?.invalid) {
      if (contactNameControl?.errors?.['minlength']) {
        this.messageService.add({
          severity: 'error',
          summary: 'Contact Name Error',
          detail: 'Contact name must be at least 2 characters',
        });
        return false;
      }

      if (contactNameControl?.errors?.['maxlength']) {
        this.messageService.add({
          severity: 'error',
          summary: 'Contact Name Error',
          detail: 'Contact name cannot exceed 100 characters',
        });
        return false;
      }
    }

    return true;
  }

  // Validate contact number
  validateContactNumber(): boolean {
    const contactNoControl = this.getContactDetailsControl('contactNo');

    if (contactNoControl?.value && contactNoControl?.invalid) {
      this.messageService.add({
        severity: 'error',
        summary: 'Contact Number Error',
        detail: 'Please enter a valid 10-digit phone number',
      });
      return false;
    }

    return true;
  }

  // Validate email
  validateEmail(): boolean {
    const emailControl = this.getContactDetailsControl('email');

    if (emailControl?.value && emailControl?.invalid) {
      this.messageService.add({
        severity: 'error',
        summary: 'Email Error',
        detail: 'Please enter a valid email address',
      });
      return false;
    }

    return true;
  }

  // Validate website URL
  validateWebsiteUrl(): boolean {
    const websiteControl = this.getContactDetailsControl('website');

    if (websiteControl?.value && websiteControl?.invalid) {
      this.messageService.add({
        severity: 'error',
        summary: 'Website URL Error',
        detail: 'Please enter a valid website URL (e.g., https://example.com)',
      });
      return false;
    }

    return true;
  }

  // Handle display time checkbox changes
  handleDisplayStartTimeChange(checked: boolean): void {
    const control = this.getDateTimeControl('startTime');
    if (control) {
      checked ? control.enable() : control.disable();
    }
  }

  handleDisplayEndTimeChange(checked: boolean): void {
    const control = this.getDateTimeControl('endTime');
    if (control) {
      checked ? control.enable() : control.disable();
    }
  }
}
