.event-page-container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.event-nav-header {
  background-color: #f8f9fa;
  padding: 1rem 0;
  margin-bottom: 1.5rem;

  // Using global back button styles

  .approval-expired-message {
    margin-right: 1rem;

    .p-badge {
      background-color: #fff3cd;
      color: #856404;
      padding: 0.5rem 0.75rem;
      border-radius: 4px;
      font-size: 0.85rem;
      font-weight: 500;
    }
  }
}

.event-details-container {
  max-width: 1200px;
  margin: 0 auto;
  padding-bottom: 3rem;
  position: relative;

  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
    margin-left: 0.5rem;

    &.pending {
      background-color: #fff8e1;
      color: #f59e0b;
    }

    &.approved {
      background-color: #e6f7ea;
      color: #10b981;
    }

    &.rejected {
      background-color: #fee2e2;
      color: #ef4444;
    }
  }

  .cover-image {
    width: 100%;
    max-width: 1920px;
    margin: 0 auto;
    height: 19rem;
    overflow: hidden;
    border-radius: 0.8rem;
    background: #e3eafc;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    position: relative;
    z-index: 1;

    .event-banner-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
      border-radius: 0.8rem;
    }
  }

  .content-container {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    margin-top: -3rem;
    max-width: 1080px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    z-index: 2;
  }

  .section {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: none;

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }

  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
  }

  .event-header {
    margin-bottom: 1.5rem;
  }

  .event-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .info-row {
    display: flex;
    flex-wrap: wrap;
    margin: -0.5rem;
  }

  .info-col {
    flex: 1 1 300px;
    padding: 0.5rem;
    min-width: 0;
  }

  .info-item {
    display: flex;
    align-items: flex-start;

    .info-icon {
      font-size: 1.2rem;
      margin-right: 0.75rem;
      margin-top: 0.25rem;
    }

    .info-content {
      flex: 1;
      min-width: 0;

      .info-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #6c757d;
      }

      .info-value {
        word-break: break-word;
      }
    }
  }

  .submitter-section {
    margin-bottom: 1.5rem;
  }

  .submitter-item {
    display: flex;
    flex-direction: column;
  }

  .submitter-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
    font-weight: 500;
  }

  .submitter-value {
    font-weight: 500;
    color: #212529;
    display: flex;
    align-items: center;
  }

  // Remove the previous section-divider style and add this
  :host ::ng-deep .p-divider {
    margin: 1.5rem 0;

    &.p-divider-horizontal:before {
      border-top: 1px solid #e9ecef;
      opacity: 0.7;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .event-details-container {
    .cover-image {
      height: 200px;
    }

    .content-container {
      padding: 1.5rem;
      margin-top: -1.5rem;
    }

    .info-col {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
}

// Add these styles for the vertical layout
.schedule-item {
  margin-bottom: 0.5rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.schedule-label {
  font-weight: 500;
  display: inline-block;
  width: 60px;
}

.info-item {
  display: flex;
  align-items: flex-start;

  .info-icon {
    font-size: 1.2rem;
    margin-right: 0.75rem;
    margin-top: 0.25rem;
    color: #6c757d;

    &.text-danger {
      color: #dc3545;
    }
  }

  .info-content {
    flex: 1;
    min-width: 0;

    .info-label {
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: #6c757d;
    }

    .info-value {
      word-break: break-word;
    }
  }
}

// Divider styling
:host ::ng-deep .p-divider {
  margin: 1.5rem 0;

  &.p-divider-horizontal:before {
    border-top: 1px solid #e9ecef;
    opacity: 0.7;
  }
}

// Event Schedule and Location styles
.event-schedule-location {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

// Schedule row with two columns
.schedule-row {
  display: flex;
  margin-bottom: 1.5rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
}

.schedule-col {
  flex: 1;
}

.schedule-label,
.location-label {
  color: #6c757d;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.schedule-value,
.location-value {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #212529;

  i {
    margin-right: 0.5rem;
    color: #6c757d;
    font-size: 1rem;
  }
}

// Location row
.location-row {
  display: flex;
  flex-direction: column;
}

// Responsive adjustments
@media (max-width: 768px) {
  .event-schedule-location {
    padding: 1rem;
  }
}

// Common styles for all content sections
.content-section {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 1.25rem;
  margin-bottom: 1rem;

  &:last-child {
    margin-bottom: 0;
  }

  // Special styling for review section to reduce white space
  &:has(.review-row) {
    padding: 1rem;
  }
}

// Common styles for all labels
.info-label {
  color: #6c757d;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

// Common styles for all values
.info-value {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #212529;

  i {
    margin-right: 0.5rem;
    color: #6c757d;
    font-size: 1rem;
  }
}

// Event header styles
.event-meta {
  margin-top: 0.75rem;

  .badge {
    margin-right: 0.5rem;
    font-weight: 500;
  }
}

// Submitter section
.submitter-row {
  display: flex;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
}

.submitter-col {
  flex: 1;
  margin-right: 2rem;

  &:last-child {
    margin-right: 0;
  }

  @media (max-width: 768px) {
    margin-right: 0;
  }
}

// Schedule row with two columns
.schedule-row {
  display: flex;
  margin-bottom: 1.5rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
}

.schedule-col {
  flex: 1;
}

// Location row
.location-row {
  display: flex;
  flex-direction: column;
}

// Contact details section
.contact-row {
  display: flex;
  flex-wrap: wrap;
  margin: -0.75rem;
}

.contact-col {
  flex: 1 1 calc(50% - 1.5rem);
  margin: 0.75rem;
  min-width: 200px;

  a {
    color: #0d6efd;

    &:hover {
      text-decoration: underline !important;
    }
  }
}

// Review section
.review-row {
  display: flex;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.5rem;
  }
}

.review-col {
  flex: 1;
  margin-right: 2rem;

  &:last-child {
    margin-right: 0;
  }

  @media (max-width: 768px) {
    margin-right: 0;
  }
}

// Event title container
.event-title-container {
  margin-bottom: 0.75rem;
}

.event-title-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;

  h3 {
    margin-right: 0.5rem;
  }
}

// Status badges
.status-badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
  vertical-align: middle;

  &.draft {
    background-color: #f5f5f5;
    color: #757575;
  }

  &.pending {
    background-color: #fff3cd;
    color: #856404;
  }

  &.approved {
    background-color: #d4edda;
    color: #155724;
  }

  &.event-started {
    background-color: #e3f2fd;
    color: #1565c0;
  }

  &.rejected {
    background-color: #f8d7da;
    color: #721c24;
  }
}

// Rejection reason
.rejection-reason {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;

  .info-label {
    color: #721c24;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .rejection-text {
    color: #721c24;
    font-weight: 500;
    display: flex;
    align-items: center;

    i {
      color: #dc3545;
      margin-right: 0.5rem;
      font-size: 1.1rem;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .content-section {
    padding: 1rem;
  }
}

// Rejection dialog styles
:host ::ng-deep .event-reject-dialog {
  .p-dialog {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
  }

  .p-dialog-content {
    padding: 1.5rem !important;
    min-height: 200px;
  }

  .p-dialog-header {
    padding: 1rem 1.5rem !important;
    border-bottom: 1px solid #e9ecef;
  }

  .p-dropdown {
    width: 100% !important;

    .p-dropdown-panel {
      z-index: 10001 !important;
      position: fixed !important;
    }
  }
}

.reject-dialog-content {
  .form-group {
    margin-bottom: 1.5rem;
    min-height: 80px; // Prevent layout shift
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .button-container {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
  }

  .p-error {
    color: #f44336;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
    min-height: 20px; // Prevent layout shift when error appears/disappears
  }
}

// Ensure dropdown overlay doesn't cause layout shifts
:host ::ng-deep .p-dropdown-panel {
  z-index: 10001 !important;
  position: fixed !important;
}

// Additional dialog stability styles
:host ::ng-deep .p-dialog-mask {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: rgba(0, 0, 0, 0.4) !important;
  z-index: 9999 !important;
}

// Prevent body scroll when dialog is open
:host ::ng-deep .p-dialog-mask.p-component-overlay {
  overflow: hidden;
}

// Ensure dialog content doesn't overflow
:host ::ng-deep .event-reject-dialog .p-dialog-content {
  overflow: visible !important;
  max-height: none !important;
}
